# 管理后台使用说明

## 概述

管理后台是一个功能完整的后台管理系统，为管理员提供用户管理、资源管理、反馈处理和系统配置等功能。

## 功能模块

### 1. 仪表盘 (`/admin`)
- **系统概览**：显示关键统计数据
- **用户统计**：总用户数、活跃用户数
- **资源统计**：总资源数、有效资源数
- **反馈统计**：待处理反馈数量
- **系统状态**：服务器状态、数据库连接状态
- **最近活动**：系统最新动态

### 2. 用户管理 (`/admin/users`)
- **用户列表**：分页显示所有用户
- **搜索筛选**：按用户名、邮箱、角色、状态筛选
- **用户操作**：
  - 创建新用户
  - 编辑用户信息
  - 重置用户密码
  - 冻结/解冻用户
  - 删除用户
- **角色管理**：普通用户、版主、管理员
- **状态管理**：正常、未激活、已冻结、已封禁

### 3. 资源管理 (`/admin/resources`)
- **资源列表**：分页显示所有资源
- **搜索筛选**：按标题、网盘类型、文件类型、状态筛选
- **时间筛选**：全部时间、最近一周/半月/一月/半年/一年
- **资源操作**：
  - 查看资源详情
  - 删除单个资源
  - 批量删除资源
- **状态监控**：有效、失效、未知状态

### 4. 反馈管理 (`/admin/feedback`)
- **反馈列表**：用户反馈的资源问题
- **筛选功能**：按失效类型、网盘类型、验证状态筛选
- **日期筛选**：按反馈时间范围筛选
- **处理操作**：
  - 查看反馈详情
  - 标记为已验证
  - 删除反馈
  - 查看相关资源

### 5. 系统配置 (`/admin/settings`)
- **基础设置**：网站名称、描述、搜索配置
- **服务器配置**：API地址、缓存设置
- **安全设置**：注册开关、邮箱验证
- **通知设置**：SMTP配置（预留）
- **数据库设置**：连接配置（预留）

## 技术特性

### 权限控制
- **认证验证**：基于JWT的用户认证
- **权限检查**：管理员权限验证
- **路由保护**：AdminGuard组件保护所有管理页面
- **自动跳转**：未登录用户自动跳转到登录页

### 响应式设计
- **移动端适配**：完整支持手机和平板设备
- **侧边栏折叠**：移动端自动折叠侧边栏
- **表格滚动**：移动端表格水平滚动
- **模态框适配**：移动端模态框全屏显示

### 主题支持
- **黑暗模式**：完整的暗黑主题支持
- **白天模式**：明亮的白天主题
- **主题切换**：一键切换主题
- **状态同步**：与前台主题状态同步

### 用户体验
- **加载状态**：所有异步操作都有加载提示
- **错误处理**：友好的错误提示和处理
- **确认对话框**：危险操作需要用户确认
- **Toast通知**：操作结果实时反馈

## 组件架构

### 布局组件
- `AdminLayout`：管理后台主布局
- `AdminDashboard`：仪表盘组件
- `DataTable`：通用数据表格组件
- `SearchFilter`：搜索筛选组件

### 功能组件
- `UserManagement`：用户管理组件
- `UserModal`：用户编辑模态框
- `ResourceManagement`：资源管理组件
- `FeedbackManagement`：反馈管理组件
- `SystemSettings`：系统配置组件

### 服务层
- `authService`：用户认证服务
- `adminResourceService`：资源管理服务
- `adminFeedbackService`：反馈管理服务

## API集成

### 认证API
- `POST /api/auth/login`：用户登录
- `GET /api/auth/me`：获取当前用户信息
- `POST /api/auth/logout`：用户登出

### 用户管理API
- `GET /api/admin/users`：获取用户列表
- `POST /api/admin/users`：创建用户
- `PUT /api/admin/users/{id}`：更新用户
- `DELETE /api/admin/users/{id}`：删除用户
- `POST /api/admin/users/{id}/freeze`：冻结用户
- `POST /api/admin/users/{id}/unfreeze`：解冻用户
- `POST /api/admin/users/{id}/reset-password`：重置密码

### 资源管理API
- `GET /api/admin/resources`：获取资源列表
- `GET /api/admin/resources/{key}`：获取资源详情
- `DELETE /api/admin/resources/{id}`：删除资源
- `POST /api/admin/resources/batch-delete`：批量删除

### 反馈管理API
- `GET /api/admin/feedback`：获取反馈列表
- `GET /api/admin/feedback/{id}`：获取反馈详情
- `DELETE /api/admin/feedback/{id}`：删除反馈
- `POST /api/admin/feedback/{id}/verify`：标记已验证

## 使用指南

### 访问管理后台
1. 使用管理员账户登录系统
2. 在导航栏用户菜单中点击"管理后台"
3. 或直接访问 `/admin` 路径

### 用户管理操作
1. **查看用户列表**：进入用户管理页面查看所有用户
2. **搜索用户**：使用搜索框按用户名或邮箱搜索
3. **筛选用户**：按角色、状态等条件筛选
4. **创建用户**：点击"新增用户"按钮创建新用户
5. **编辑用户**：点击用户行的"编辑"按钮修改信息
6. **管理状态**：冻结/解冻用户账户
7. **重置密码**：为用户重置新密码

### 资源管理操作
1. **查看资源**：浏览所有网盘资源
2. **搜索资源**：按标题关键词搜索
3. **筛选资源**：按网盘类型、文件类型、状态筛选
4. **删除资源**：删除单个或批量删除资源
5. **查看详情**：点击"查看"按钮查看资源详情

### 反馈处理操作
1. **查看反馈**：查看用户提交的资源问题反馈
2. **筛选反馈**：按失效类型、网盘类型筛选
3. **处理反馈**：标记反馈为已验证
4. **删除反馈**：删除无效或重复的反馈

## 注意事项

1. **权限要求**：只有管理员角色才能访问管理后台
2. **数据安全**：删除操作不可恢复，请谨慎操作
3. **批量操作**：批量删除前请确认选择的数据
4. **网络要求**：需要稳定的网络连接访问后端API
5. **浏览器兼容**：建议使用现代浏览器以获得最佳体验

## 故障排除

### 常见问题
1. **无法访问管理后台**：检查用户角色是否为管理员
2. **数据加载失败**：检查网络连接和后端服务状态
3. **操作失败**：查看浏览器控制台错误信息
4. **主题显示异常**：清除浏览器缓存重新加载

### 技术支持
如遇到技术问题，请检查：
1. 浏览器控制台错误信息
2. 网络请求状态
3. 后端服务运行状态
4. API接口响应内容
