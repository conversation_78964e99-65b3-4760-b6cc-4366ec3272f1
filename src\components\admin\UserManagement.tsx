"use client";

import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/components/ToastProvider";
import {
  getUserList,
  createUser,
  updateUser,
  deleteUser,
  freezeUser,
  unfreezeUser,
  resetUserPassword,
  User,
  UserListResponse,
} from "@/services/authService";
import DataTable, { Column } from "./DataTable";
import SearchFilter, { FilterField } from "./SearchFilter";
import UserModal from "./UserModal";

export default function UserManagement() {
  const { showToast } = useToast();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);

  const loadUsers = useCallback(async () => {
    setLoading(true);
    try {
      const result: UserListResponse = await getUserList(
        currentPage,
        20,
        searchTerm || undefined,
        roleFilter || undefined,
        statusFilter || undefined
      );

      if (result.success) {
        setUsers(result.data.users);
        setTotalUsers(result.data.total);
      } else {
        showToast(result.message || "获取用户列表失败", "error");
      }
    } catch {
      showToast("获取用户列表失败", "error");
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm, roleFilter, statusFilter, showToast]);

  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  const handleSearch = () => {
    setCurrentPage(1);
    loadUsers();
  };

  const handleCreateUser = async (userData: any) => {
    try {
      const result = await createUser(userData);
      if (result.success) {
        showToast(result.message, "success");
        setShowCreateModal(false);
        loadUsers();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("创建用户失败", "error");
    }
  };

  const handleUpdateUser = async (userId: number, updates: Partial<User>) => {
    try {
      const result = await updateUser(userId, updates);
      if (result.success) {
        showToast(result.message, "success");
        setEditingUser(null);
        loadUsers();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("更新用户失败", "error");
    }
  };

  const handleDeleteUser = async (userId: number, username: string) => {
    if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复。`)) {
      return;
    }

    try {
      const result = await deleteUser(userId);
      if (result.success) {
        showToast(result.message, "success");
        loadUsers();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("删除用户失败", "error");
    }
  };

  const handleFreezeUser = async (
    userId: number,
    username: string,
    freeze: boolean
  ) => {
    const action = freeze ? "冻结" : "解冻";
    if (!confirm(`确定要${action}用户 "${username}" 吗？`)) {
      return;
    }

    try {
      const result = freeze
        ? await freezeUser(userId)
        : await unfreezeUser(userId);
      if (result.success) {
        showToast(result.message, "success");
        loadUsers();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast(`${action}用户失败`, "error");
    }
  };

  const handleResetPassword = async (userId: number, username: string) => {
    const newPassword = prompt(`为用户 "${username}" 设置新密码：`);
    if (!newPassword) return;

    if (newPassword.length < 6) {
      showToast("密码至少需要6个字符", "error");
      return;
    }

    try {
      const result = await resetUserPassword(userId, newPassword);
      if (result.success) {
        showToast(result.message, "success");
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("重置密码失败", "error");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  const getRoleText = (role: string) => {
    const roleMap: { [key: string]: string } = {
      admin: "管理员",
      user: "普通用户",
      moderator: "版主",
    };
    return roleMap[role] || role;
  };

  const getStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      active: "正常",
      inactive: "未激活",
      suspended: "已冻结",
      banned: "已封禁",
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      active: "text-green-600 dark:text-green-400",
      inactive: "text-yellow-600 dark:text-yellow-400",
      suspended: "text-red-600 dark:text-red-400",
      banned: "text-red-800 dark:text-red-300",
    };
    return colorMap[status] || "text-gray-600 dark:text-gray-400";
  };

  const filters: FilterField[] = [
    {
      key: "role",
      label: "角色筛选",
      type: "select",
      value: roleFilter,
      onChange: setRoleFilter,
      options: [
        { label: "管理员", value: "admin" },
        { label: "版主", value: "moderator" },
        { label: "普通用户", value: "user" },
      ],
    },
    {
      key: "status",
      label: "状态筛选",
      type: "select",
      value: statusFilter,
      onChange: setStatusFilter,
      options: [
        { label: "正常", value: "active" },
        { label: "未激活", value: "inactive" },
        { label: "已冻结", value: "suspended" },
        { label: "已封禁", value: "banned" },
      ],
    },
  ];

  const columns: Column<User>[] = [
    {
      key: "username",
      title: "用户信息",
      render: (_, record) => (
        <div>
          <div className="text-sm font-medium text-foreground">
            {record.username}
          </div>
          <div className="text-sm text-secondary-text">{record.email}</div>
        </div>
      ),
    },
    {
      key: "role",
      title: "角色",
      render: (role) => (
        <span className="text-sm text-foreground">{getRoleText(role)}</span>
      ),
    },
    {
      key: "status",
      title: "状态",
      render: (status) => (
        <span className={`text-sm ${getStatusColor(status)}`}>
          {getStatusText(status)}
        </span>
      ),
    },
    {
      key: "created_at",
      title: "注册时间",
      render: (date) => (
        <span className="text-sm text-secondary-text">{formatDate(date)}</span>
      ),
    },
    {
      key: "last_login",
      title: "最后登录",
      render: (date) => (
        <span className="text-sm text-secondary-text">
          {date ? formatDate(date) : "从未登录"}
        </span>
      ),
    },
    {
      key: "actions",
      title: "操作",
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setEditingUser(record)}
          >
            编辑
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleResetPassword(record.id, record.username)}
          >
            重置密码
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() =>
              handleFreezeUser(
                record.id,
                record.username,
                record.status === "active"
              )
            }
          >
            {record.status === "active" ? "冻结" : "解冻"}
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleDeleteUser(record.id, record.username)}
            className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
          >
            删除
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-foreground mb-2">用户管理</h1>
        <p className="text-secondary-text">管理系统用户账户</p>
      </div>

      {/* 搜索和筛选 */}
      <SearchFilter
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        onSearch={handleSearch}
        filters={filters}
        actions={
          <Button onClick={() => setShowCreateModal(true)}>新增用户</Button>
        }
        className="mb-6"
      />

      {/* 用户列表 */}
      <DataTable
        columns={columns}
        data={users}
        loading={loading}
        pagination={{
          current: currentPage,
          total: totalUsers,
          pageSize: 20,
          onChange: setCurrentPage,
        }}
        emptyText="暂无用户数据"
      />

      {/* 创建用户模态框 */}
      {showCreateModal && (
        <UserModal
          title="创建新用户"
          onSubmit={handleCreateUser}
          onCancel={() => setShowCreateModal(false)}
        />
      )}

      {/* 编辑用户模态框 */}
      {editingUser && (
        <UserModal
          title="编辑用户"
          user={editingUser}
          onSubmit={(data: any) => handleUpdateUser(editingUser.id, data)}
          onCancel={() => setEditingUser(null)}
        />
      )}
    </div>
  );
}
