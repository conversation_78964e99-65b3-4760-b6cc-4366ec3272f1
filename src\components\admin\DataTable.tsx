"use client";

import { ReactNode } from "react";
import { Button } from "@/components/ui/Button";

export interface Column<T> {
  key: keyof T | string;
  title: string;
  render?: (value: any, record: T, index: number) => ReactNode;
  width?: string;
  align?: "left" | "center" | "right";
  sortable?: boolean;
}

export interface DataTableProps<T> {
  columns: Column<T>[];
  data: T[];
  loading?: boolean;
  pagination?: {
    current: number;
    total: number;
    pageSize: number;
    onChange: (page: number) => void;
  };
  rowKey?: keyof T | ((record: T) => string | number);
  onRow?: (record: T, index: number) => {
    onClick?: () => void;
    className?: string;
  };
  emptyText?: string;
  className?: string;
}

export default function DataTable<T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  pagination,
  rowKey = "id" as keyof T,
  onRow,
  emptyText = "暂无数据",
  className = "",
}: DataTableProps<T>) {
  const getRowKey = (record: T, index: number): string | number => {
    if (typeof rowKey === "function") {
      return rowKey(record);
    }
    return record[rowKey] || index;
  };

  const getValue = (record: T, key: keyof T | string): any => {
    if (typeof key === "string" && key.includes(".")) {
      // 支持嵌套属性，如 "user.name"
      return key.split(".").reduce((obj, k) => obj?.[k], record);
    }
    return record[key as keyof T];
  };

  const getAlignClass = (align?: "left" | "center" | "right") => {
    switch (align) {
      case "center":
        return "text-center";
      case "right":
        return "text-right";
      default:
        return "text-left";
    }
  };

  if (loading) {
    return (
      <div className={`bg-card-background rounded-lg shadow-lg border border-border-color ${className}`}>
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-button-background mx-auto mb-4"></div>
          <p className="text-secondary-text">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-card-background rounded-lg shadow-lg border border-border-color ${className}`}>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-hover-background">
            <tr>
              {columns.map((column, index) => (
                <th
                  key={index}
                  className={`px-6 py-3 text-xs font-medium text-secondary-text uppercase tracking-wider ${getAlignClass(
                    column.align
                  )}`}
                  style={{ width: column.width }}
                >
                  {column.title}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-border-color">
            {data.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length}
                  className="px-6 py-12 text-center text-secondary-text"
                >
                  {emptyText}
                </td>
              </tr>
            ) : (
              data.map((record, index) => {
                const rowProps = onRow?.(record, index) || {};
                return (
                  <tr
                    key={getRowKey(record, index)}
                    className={`hover:bg-hover-background transition-colors ${
                      rowProps.className || ""
                    } ${rowProps.onClick ? "cursor-pointer" : ""}`}
                    onClick={rowProps.onClick}
                  >
                    {columns.map((column, colIndex) => {
                      const value = getValue(record, column.key);
                      return (
                        <td
                          key={colIndex}
                          className={`px-6 py-4 whitespace-nowrap text-sm ${getAlignClass(
                            column.align
                          )}`}
                          style={{ width: column.width }}
                        >
                          {column.render
                            ? column.render(value, record, index)
                            : value}
                        </td>
                      );
                    })}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* 分页 */}
      {pagination && pagination.total > pagination.pageSize && (
        <div className="px-6 py-4 border-t border-border-color">
          <div className="flex items-center justify-between">
            <div className="text-sm text-secondary-text">
              第 {pagination.current} 页，共{" "}
              {Math.ceil(pagination.total / pagination.pageSize)} 页，总计{" "}
              {pagination.total} 条记录
            </div>
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="outline"
                disabled={pagination.current === 1}
                onClick={() => pagination.onChange(pagination.current - 1)}
              >
                上一页
              </Button>
              <Button
                size="sm"
                variant="outline"
                disabled={
                  pagination.current >=
                  Math.ceil(pagination.total / pagination.pageSize)
                }
                onClick={() => pagination.onChange(pagination.current + 1)}
              >
                下一页
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
