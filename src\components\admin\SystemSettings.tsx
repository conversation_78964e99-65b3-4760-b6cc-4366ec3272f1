"use client";

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { useToast } from "@/components/ToastProvider";
import {
  Cog6ToothIcon,
  ServerIcon,
  DatabaseIcon,
  ShieldCheckIcon,
  BellIcon,
} from "@heroicons/react/24/outline";

interface SettingSection {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

const settingSections: SettingSection[] = [
  {
    id: "general",
    title: "基础设置",
    description: "网站基本信息和配置",
    icon: Cog6ToothIcon,
  },
  {
    id: "server",
    title: "服务器配置",
    description: "API服务器和缓存配置",
    icon: ServerIcon,
  },
  {
    id: "database",
    title: "数据库设置",
    description: "数据库连接和优化配置",
    icon: DatabaseIcon,
  },
  {
    id: "security",
    title: "安全设置",
    description: "用户认证和权限配置",
    icon: ShieldCheckIcon,
  },
  {
    id: "notifications",
    title: "通知设置",
    description: "邮件和消息通知配置",
    icon: BellIcon,
  },
];

export default function SystemSettings() {
  const { showToast } = useToast();
  const [activeSection, setActiveSection] = useState("general");
  const [loading, setLoading] = useState(false);

  // 模拟配置数据
  const [settings, setSettings] = useState({
    siteName: "97盘搜",
    siteDescription: "专业的网盘资源搜索平台",
    apiUrl: "http://127.0.0.1:9999",
    cacheTimeout: "300",
    maxSearchResults: "100",
    enableRegistration: true,
    enableEmailVerification: true,
    smtpHost: "",
    smtpPort: "587",
    smtpUser: "",
    smtpPassword: "",
  });

  const handleSaveSettings = async () => {
    setLoading(true);
    try {
      // 这里应该调用实际的API保存配置
      await new Promise(resolve => setTimeout(resolve, 1000));
      showToast("配置保存成功", "success");
    } catch (error) {
      showToast("配置保存失败", "error");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (key: string, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          网站名称
        </label>
        <Input
          type="text"
          value={settings.siteName}
          onChange={(e) => handleInputChange("siteName", e.target.value)}
          placeholder="请输入网站名称"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          网站描述
        </label>
        <Input
          type="text"
          value={settings.siteDescription}
          onChange={(e) => handleInputChange("siteDescription", e.target.value)}
          placeholder="请输入网站描述"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          最大搜索结果数
        </label>
        <Input
          type="number"
          value={settings.maxSearchResults}
          onChange={(e) => handleInputChange("maxSearchResults", e.target.value)}
          placeholder="请输入最大搜索结果数"
        />
      </div>
    </div>
  );

  const renderServerSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          API服务器地址
        </label>
        <Input
          type="text"
          value={settings.apiUrl}
          onChange={(e) => handleInputChange("apiUrl", e.target.value)}
          placeholder="请输入API服务器地址"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          缓存超时时间（秒）
        </label>
        <Input
          type="number"
          value={settings.cacheTimeout}
          onChange={(e) => handleInputChange("cacheTimeout", e.target.value)}
          placeholder="请输入缓存超时时间"
        />
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <input
          type="checkbox"
          id="enableRegistration"
          checked={settings.enableRegistration}
          onChange={(e) => handleInputChange("enableRegistration", e.target.checked)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor="enableRegistration" className="text-sm font-medium text-foreground">
          启用用户注册
        </label>
      </div>
      <div className="flex items-center space-x-3">
        <input
          type="checkbox"
          id="enableEmailVerification"
          checked={settings.enableEmailVerification}
          onChange={(e) => handleInputChange("enableEmailVerification", e.target.checked)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor="enableEmailVerification" className="text-sm font-medium text-foreground">
          启用邮箱验证
        </label>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          SMTP服务器
        </label>
        <Input
          type="text"
          value={settings.smtpHost}
          onChange={(e) => handleInputChange("smtpHost", e.target.value)}
          placeholder="请输入SMTP服务器地址"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          SMTP端口
        </label>
        <Input
          type="number"
          value={settings.smtpPort}
          onChange={(e) => handleInputChange("smtpPort", e.target.value)}
          placeholder="请输入SMTP端口"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          SMTP用户名
        </label>
        <Input
          type="text"
          value={settings.smtpUser}
          onChange={(e) => handleInputChange("smtpUser", e.target.value)}
          placeholder="请输入SMTP用户名"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          SMTP密码
        </label>
        <Input
          type="password"
          value={settings.smtpPassword}
          onChange={(e) => handleInputChange("smtpPassword", e.target.value)}
          placeholder="请输入SMTP密码"
        />
      </div>
    </div>
  );

  const renderDatabaseSettings = () => (
    <div className="space-y-6">
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <DatabaseIcon className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
          <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
            数据库配置
          </span>
        </div>
        <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-2">
          数据库配置功能正在开发中，敬请期待。
        </p>
      </div>
    </div>
  );

  const renderSettingContent = () => {
    switch (activeSection) {
      case "general":
        return renderGeneralSettings();
      case "server":
        return renderServerSettings();
      case "database":
        return renderDatabaseSettings();
      case "security":
        return renderSecuritySettings();
      case "notifications":
        return renderNotificationSettings();
      default:
        return renderGeneralSettings();
    }
  };

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-foreground mb-2">系统配置</h1>
        <p className="text-secondary-text">管理系统参数和配置选项</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 配置分类 */}
        <div className="lg:col-span-1">
          <div className="bg-card-background rounded-lg shadow-lg border border-border-color p-4">
            <h3 className="text-lg font-semibold text-foreground mb-4">配置分类</h3>
            <nav className="space-y-2">
              {settingSections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full text-left p-3 rounded-lg transition-colors ${
                    activeSection === section.id
                      ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white"
                      : "hover:bg-hover-background text-foreground"
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <section.icon
                      className={`h-5 w-5 ${
                        activeSection === section.id ? "text-white" : "text-secondary-text"
                      }`}
                    />
                    <div>
                      <div className="font-medium">{section.title}</div>
                      <div
                        className={`text-xs ${
                          activeSection === section.id
                            ? "text-blue-100"
                            : "text-secondary-text"
                        }`}
                      >
                        {section.description}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* 配置内容 */}
        <div className="lg:col-span-3">
          <div className="bg-card-background rounded-lg shadow-lg border border-border-color p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-foreground">
                {settingSections.find(s => s.id === activeSection)?.title}
              </h3>
              <Button
                onClick={handleSaveSettings}
                disabled={loading}
              >
                {loading ? "保存中..." : "保存配置"}
              </Button>
            </div>

            {renderSettingContent()}
          </div>
        </div>
      </div>
    </div>
  );
}
