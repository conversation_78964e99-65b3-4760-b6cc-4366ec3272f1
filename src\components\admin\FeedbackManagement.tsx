"use client";

import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/components/ToastProvider";
import {
  getAdminFeedbackList,
  markFeedbackAsVerified,
  deleteFeedback,
  AdminFeedback,
  AdminFeedbackListResponse,
} from "@/services/adminFeedbackService";
import DataTable, { Column } from "./DataTable";
import SearchFilter, { FilterField } from "./SearchFilter";

export default function FeedbackManagement() {
  const { showToast } = useToast();
  const [feedback, setFeedback] = useState<AdminFeedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [invalidTypeFilter, setInvalidTypeFilter] = useState("");
  const [panTypeFilter, setPanTypeFilter] = useState("");
  const [isVerifiedFilter, setIsVerifiedFilter] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalFeedback, setTotalFeedback] = useState(0);

  const loadFeedback = useCallback(async () => {
    setLoading(true);
    try {
      const result: AdminFeedbackListResponse = await getAdminFeedbackList(
        currentPage,
        20,
        invalidTypeFilter ? parseInt(invalidTypeFilter) : undefined,
        panTypeFilter ? parseInt(panTypeFilter) : undefined,
        isVerifiedFilter ? isVerifiedFilter === "true" : undefined,
        searchTerm || undefined,
        startDate || undefined,
        endDate || undefined
      );

      if (result.success) {
        setFeedback(result.data.feedback);
        setTotalPages(result.data.total_pages);
        setTotalFeedback(result.data.total);
      } else {
        showToast(result.message || "获取反馈列表失败", "error");
      }
    } catch {
      showToast("获取反馈列表失败", "error");
    } finally {
      setLoading(false);
    }
  }, [
    currentPage,
    searchTerm,
    invalidTypeFilter,
    panTypeFilter,
    isVerifiedFilter,
    startDate,
    endDate,
    showToast,
  ]);

  useEffect(() => {
    loadFeedback();
  }, [loadFeedback]);

  const handleSearch = () => {
    setCurrentPage(1);
    loadFeedback();
  };

  const handleMarkAsVerified = async (feedbackId: number) => {
    try {
      const result = await markFeedbackAsVerified(feedbackId);
      if (result.success) {
        showToast(result.message, "success");
        loadFeedback();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("标记反馈失败", "error");
    }
  };

  const handleDeleteFeedback = async (feedbackId: number) => {
    if (!confirm("确定要删除这条反馈吗？此操作不可恢复。")) {
      return;
    }

    try {
      const result = await deleteFeedback(feedbackId);
      if (result.success) {
        showToast(result.message, "success");
        loadFeedback();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("删除反馈失败", "error");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  const getPanTypeName = (panType: number) => {
    const panTypeMap: { [key: number]: string } = {
      1: "百度网盘",
      2: "夸克网盘",
      3: "阿里云盘",
      4: "迅雷网盘",
    };
    return panTypeMap[panType] || "其他";
  };

  const getInvalidTypeName = (invalidType: number) => {
    const invalidTypeMap: { [key: number]: string } = {
      1: "链接错误",
      2: "资源不存在",
      3: "需要密码",
      4: "其他问题",
    };
    return invalidTypeMap[invalidType] || "未知";
  };

  const filters: FilterField[] = [
    {
      key: "invalidType",
      label: "失效类型",
      type: "select",
      value: invalidTypeFilter,
      onChange: setInvalidTypeFilter,
      options: [
        { label: "链接错误", value: "1" },
        { label: "资源不存在", value: "2" },
        { label: "需要密码", value: "3" },
        { label: "其他问题", value: "4" },
      ],
    },
    {
      key: "panType",
      label: "网盘类型",
      type: "select",
      value: panTypeFilter,
      onChange: setPanTypeFilter,
      options: [
        { label: "百度网盘", value: "1" },
        { label: "夸克网盘", value: "2" },
        { label: "阿里云盘", value: "3" },
        { label: "迅雷网盘", value: "4" },
      ],
    },
    {
      key: "isVerified",
      label: "验证状态",
      type: "select",
      value: isVerifiedFilter,
      onChange: setIsVerifiedFilter,
      options: [
        { label: "已验证", value: "true" },
        { label: "未验证", value: "false" },
      ],
    },
    {
      key: "startDate",
      label: "开始日期",
      type: "date",
      value: startDate,
      onChange: setStartDate,
    },
    {
      key: "endDate",
      label: "结束日期",
      type: "date",
      value: endDate,
      onChange: setEndDate,
    },
  ];

  const columns: Column<AdminFeedback>[] = [
    {
      key: "resource_title",
      title: "资源信息",
      render: (_, record) => (
        <div>
          <div className="text-sm font-medium text-foreground truncate max-w-xs">
            {record.resource_title || "未知资源"}
          </div>
          <div className="text-xs text-secondary-text">
            ID: {record.resource_id}
          </div>
        </div>
      ),
    },
    {
      key: "pan_type",
      title: "网盘类型",
      render: (panType) => (
        <span className="text-sm text-foreground">
          {getPanTypeName(panType)}
        </span>
      ),
    },
    {
      key: "invalid_type",
      title: "失效类型",
      render: (invalidType) => (
        <span className="text-sm text-foreground">
          {getInvalidTypeName(invalidType)}
        </span>
      ),
    },
    {
      key: "description",
      title: "问题描述",
      render: (description) => (
        <div className="text-sm text-foreground max-w-xs truncate">
          {description || "无描述"}
        </div>
      ),
    },
    {
      key: "contact_info",
      title: "联系方式",
      render: (contactInfo) => (
        <div className="text-sm text-secondary-text max-w-xs truncate">
          {contactInfo || "未提供"}
        </div>
      ),
    },
    {
      key: "is_verified",
      title: "验证状态",
      render: (isVerified) => (
        <span
          className={`text-sm ${
            isVerified
              ? "text-green-600 dark:text-green-400"
              : "text-yellow-600 dark:text-yellow-400"
          }`}
        >
          {isVerified ? "已验证" : "未验证"}
        </span>
      ),
    },
    {
      key: "created_at",
      title: "反馈时间",
      render: (date) => (
        <span className="text-sm text-secondary-text">
          {formatDate(date)}
        </span>
      ),
    },
    {
      key: "actions",
      title: "操作",
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          {!record.is_verified && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleMarkAsVerified(record.id)}
              className="text-green-600 hover:text-green-700 border-green-300 hover:border-green-400"
            >
              标记已验证
            </Button>
          )}
          <Button
            size="sm"
            variant="outline"
            onClick={() => window.open(`/resources/${record.resource_id}`, '_blank')}
          >
            查看资源
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleDeleteFeedback(record.id)}
            className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
          >
            删除
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-foreground mb-2">反馈管理</h1>
        <p className="text-secondary-text">查看和处理用户反馈的资源问题信息</p>
      </div>

      {/* 搜索和筛选 */}
      <SearchFilter
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        onSearch={handleSearch}
        filters={filters}
        className="mb-6"
      />

      {/* 反馈列表 */}
      <DataTable
        columns={columns}
        data={feedback}
        loading={loading}
        pagination={{
          current: currentPage,
          total: totalFeedback,
          pageSize: 20,
          onChange: setCurrentPage,
        }}
        emptyText="暂无反馈数据"
      />
    </div>
  );
}
