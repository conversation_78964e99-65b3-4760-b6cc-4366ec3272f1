"use client";

import { useState, ReactNode } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useTheme } from "next-themes";
import {
  Bars3Icon,
  XMarkIcon,
  HomeIcon,
  UsersIcon,
  FolderIcon,
  ChatBubbleLeftRightIcon,
  Cog6ToothIcon,
  ArrowLeftOnRectangleIcon,
} from "@heroicons/react/24/outline";
import { SunIcon, MoonIcon } from "@heroicons/react/24/solid";
import { useAuth } from "@/hooks/useAuth";
import { useMounted } from "@/hooks/use-mounted";

interface AdminLayoutProps {
  children: ReactNode;
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const navigation: NavItem[] = [
  {
    name: "仪表盘",
    href: "/admin",
    icon: HomeIcon,
    description: "系统概览和统计信息",
  },
  {
    name: "用户管理",
    href: "/admin/users",
    icon: UsersIcon,
    description: "管理系统用户账户",
  },
  {
    name: "资源管理",
    href: "/admin/resources",
    icon: FolderIcon,
    description: "管理网盘资源内容",
  },
  {
    name: "反馈管理",
    href: "/admin/feedback",
    icon: ChatBubbleLeftRightIcon,
    description: "处理用户反馈信息",
  },
  {
    name: "系统配置",
    href: "/admin/settings",
    icon: Cog6ToothIcon,
    description: "系统参数配置管理",
  },
];

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();
  const { theme, setTheme } = useTheme();
  const { user, logout } = useAuth();
  const mounted = useMounted();

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  const handleLogout = async () => {
    if (confirm("确定要退出登录吗？")) {
      await logout();
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* 移动端侧边栏遮罩 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="absolute inset-0 bg-black opacity-50"></div>
        </div>
      )}

      {/* 侧边栏 */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-card-background border-r border-border-color transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Logo 区域 */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-border-color">
            <Link href="/admin" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">管</span>
              </div>
              <span className="text-lg font-semibold text-foreground">
                管理后台
              </span>
            </Link>
            <button
              type="button"
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-1 rounded-md hover:bg-hover-background"
              aria-label="关闭侧边栏"
              title="关闭侧边栏"
            >
              <XMarkIcon className="h-6 w-6 text-foreground" />
            </button>
          </div>

          {/* 导航菜单 */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    isActive
                      ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white"
                      : "text-foreground hover:bg-hover-background"
                  }`}
                  onClick={() => setSidebarOpen(false)}
                >
                  <item.icon
                    className={`mr-3 h-5 w-5 ${
                      isActive ? "text-white" : "text-secondary-text"
                    }`}
                  />
                  <div>
                    <div className="font-medium">{item.name}</div>
                    <div
                      className={`text-xs ${
                        isActive
                          ? "text-blue-100"
                          : "text-secondary-text group-hover:text-foreground"
                      }`}
                    >
                      {item.description}
                    </div>
                  </div>
                </Link>
              );
            })}
          </nav>

          {/* 用户信息和操作 */}
          <div className="border-t border-border-color p-4">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white font-medium text-sm">
                  {user?.username?.charAt(0).toUpperCase() || "A"}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-foreground truncate">
                  {user?.username || "管理员"}
                </div>
                <div className="text-xs text-secondary-text">
                  {user?.role === "admin" ? "系统管理员" : "管理员"}
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              {/* 主题切换 */}
              {mounted && (
                <button
                  type="button"
                  onClick={toggleTheme}
                  className="p-2 rounded-md hover:bg-hover-background transition-colors"
                  aria-label="切换主题"
                  title="切换主题"
                >
                  {theme === "dark" ? (
                    <SunIcon className="h-5 w-5 text-foreground" />
                  ) : (
                    <MoonIcon className="h-5 w-5 text-foreground" />
                  )}
                </button>
              )}

              {/* 退出登录 */}
              <button
                type="button"
                onClick={handleLogout}
                className="p-2 rounded-md hover:bg-hover-background transition-colors text-red-600 hover:text-red-700"
                aria-label="退出登录"
                title="退出登录"
              >
                <ArrowLeftOnRectangleIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="lg:pl-64">
        {/* 顶部导航栏 */}
        <div className="sticky top-0 z-30 bg-nav-background backdrop-blur-sm border-b border-border-color">
          <div className="flex items-center justify-between h-16 px-6">
            <button
              type="button"
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden p-2 rounded-md hover:bg-hover-background"
              aria-label="打开侧边栏"
              title="打开侧边栏"
            >
              <Bars3Icon className="h-6 w-6 text-foreground" />
            </button>

            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="text-sm text-secondary-text hover:text-foreground transition-colors"
              >
                返回前台
              </Link>
            </div>
          </div>
        </div>

        {/* 页面内容 */}
        <main className="flex-1">{children}</main>
      </div>
    </div>
  );
}
