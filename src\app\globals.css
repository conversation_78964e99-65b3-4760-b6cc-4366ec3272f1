@import "tailwindcss";

/* 苹果风格的颜色变量 */
:root {
  /* 日间模式 */
  --background: #ffffff;
  --foreground: #18181c;
  --secondary-text: #5c5c66;
  --nav-background: rgba(255, 255, 255, 0.85);
  --card-background: #f8f9fb;
  --hover-background: #e4e6eb;
  --border-color: #d2d2d7;
  --link-color: #0056b3;
  --button-background: #0071e3;
  --button-hover: #0056b3;
  --secondary-button-background: #6b7280;
  --secondary-button-text: #ffffff;
  --secondary-button-hover: #4b5563;
  --footer-background: #f5f5f7;
  --footer-border: #d2d2d7;
  --footer-link: #0066cc;
  --footer-link-hover: #0077ed;
}

.dark {
  /* 暗黑模式 - 采用柔和的黑灰色配色 */
  --background: #1a1a1f;
  --foreground: #f0f0f5;
  --secondary-text: #b0b3b8;
  --nav-background: rgba(32, 32, 38, 0.92);
  --card-background: #242428;
  --hover-background: #2c2c34;
  --border-color: #3a3a42;
  --link-color: #4da3ff;
  --button-background: #2997ff;
  --button-hover: #0077ed;
  --secondary-button-background: #4b5563;
  --secondary-button-text: #f9fafb;
  --secondary-button-hover: #374151;
  --footer-background: #222226;
  --footer-border: #35353d;
  --footer-link: #2997ff;
  --footer-link-hover: #56abff;
}

/* 基础样式 */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text",
    "Helvetica Neue", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: -0.022em;
}

/* 苹果风格的文本样式 */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 暗黑模式样式覆盖 */
.dark .bg-white {
  background-color: var(--card-background);
}

.dark .text-gray-600 {
  color: var(--secondary-text);
}

.dark .text-gray-900 {
  color: var(--foreground);
}

.dark .border-gray-200 {
  border-color: var(--border-color);
}

.dark .bg-gray-100 {
  background-color: var(--hover-background);
}

.dark .hover\:bg-gray-100:hover {
  background-color: var(--hover-background);
}

/* 确保筛选组件在暗黑模式下文字为白色 */
.dark .filter-bar label {
  color: white !important;
}

.dark .filter-bar select,
.dark select option,
.dark #pan-type-select option,
.dark #file-type-select option,
.dark #time-filter-select option {
  color: white !important;
  background-color: rgb(55 65 81) !important; /* gray-700 */
}

/* 资源失效反馈弹窗中的下拉列表样式 */
.dark select option {
  background-color: rgb(55 65 81) !important; /* gray-700 */
  color: white !important;
}

/* 更具体的选择器确保弹窗中的下拉列表样式正确 */
.dark [role="dialog"] select,
.dark [role="dialog"] select option {
  background-color: rgb(55 65 81) !important; /* gray-700 */
  color: white !important;
}

/* 精确搜索按钮文字颜色 - 使用更精确的选择器 */
.dark .filter-bar .inline-flex span {
  color: white !important;
}

/* 移除暗黑模式下筛选组件的聚焦效果和文字装饰 */
.dark .filter-bar select:focus,
.dark .filter-bar label:focus,
.dark #pan-type-select:focus,
.dark #file-type-select:focus,
.dark #time-filter-select:focus {
  outline: none !important;
  box-shadow: none !important;
  text-decoration: none !important;
  border-color: var(--border-color) !important;
}

/* 确保标签文字没有任何装饰效果 */
.dark .filter-bar label {
  text-decoration: none !important;
  border-bottom: none !important;
  box-shadow: none !important;
}

/* 毛玻璃效果 */
.glass-effect {
  -webkit-backdrop-filter: saturate(180%) blur(20px);
  backdrop-filter: saturate(180%) blur(20px);
}

/* 平滑过渡 */
* {
  transition: background-color 0.3s ease, color 0.3s ease,
    border-color 0.3s ease;
}

/* 导航栏样式 */
.nav-transparent {
  background-color: transparent;
}

.nav-scrolled {
  background-color: var(--nav-background);
}

/* 资源数字值样式 */
.numeric-value {
  color: #18181c;
  font-weight: 700;
}

.dark .numeric-value {
  color: #f5f6fa;
}

/* 黑暗模式下的文字颜色 */
.dark .dark-text-white {
  color: white !important;
}

/* 滚动按钮样式 */
.scroll-button-bg {
  background-color: rgba(229, 231, 235, 0.8);
}

.dark .scroll-button-bg {
  background-color: rgba(55, 65, 81, 0.8);
}

/* 页脚样式 */
.apple-footer {
  background-color: var(--footer-background);
  color: var(--secondary-text);
  font-size: 0.85rem;
  border-top: 1px solid var(--footer-border);
  padding: 1rem 0;
}

.apple-footer h3 {
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  color: var(--foreground);
}

.apple-footer-links a {
  color: var(--footer-link);
  transition: color 0.2s ease;
  display: inline-block;
  padding: 0.2rem 0;
}

.apple-footer-links a:hover {
  color: var(--footer-link-hover);
  text-decoration: none;
}

.apple-footer-copyright {
  font-size: 0.75rem;
  margin-top: 0.5rem;
}

/* 搜索提示按钮在暗黑模式下为灰白色 */
.dark .search-tips-btn {
  color: #e5e7eb !important;
}

/* 资源卡片反馈按钮特定样式 - 覆盖全局规则 */
.dark .resource-feedback-btn {
  background-color: #f9f9f9 !important;
  color: #374151 !important;
}

.dark .resource-feedback-btn:hover {
  background-color: #e5e7eb !important;
}

/* 网盘类型标签特定样式 - 覆盖全局规则 */
.dark .pan-type-label {
  background-color: #f9f9f9 !important;
  color: #374151 !important;
}

/* 确保反馈按钮内的span文字颜色正确 */
.dark .resource-feedback-btn span {
  color: #374151 !important;
}

/* 管理后台专用样式 */
.admin-layout {
  min-height: 100vh;
}

/* 管理后台侧边栏样式 */
.admin-sidebar {
  transition: transform 0.3s ease-in-out;
}

/* 管理后台表格响应式 */
@media (max-width: 768px) {
  .admin-table-container {
    overflow-x: auto;
  }

  .admin-table th,
  .admin-table td {
    min-width: 120px;
    white-space: nowrap;
  }

  .admin-table .actions-column {
    min-width: 200px;
  }
}

/* 管理后台模态框移动端适配 */
@media (max-width: 640px) {
  .admin-modal {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }
}

/* 管理后台卡片网格响应式 */
.admin-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 640px) {
  .admin-stats-grid {
    grid-template-columns: 1fr;
  }
}

/* 管理后台搜索筛选响应式 */
.admin-filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

@media (max-width: 640px) {
  .admin-filter-grid {
    grid-template-columns: 1fr;
  }
}
