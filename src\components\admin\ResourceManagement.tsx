"use client";

import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/components/ToastProvider";
import {
  getAdminResourceList,
  deleteAdminResource,
  batchDeleteAdminResources,
  AdminResource,
  AdminResourceListResponse,
} from "@/services/adminResourceService";
import DataTable, { Column } from "./DataTable";
import SearchFilter, { FilterField } from "./SearchFilter";

export default function ResourceManagement() {
  const { showToast } = useToast();
  const [resources, setResources] = useState<AdminResource[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [panTypeFilter, setPanTypeFilter] = useState("");
  const [fileTypeFilter, setFileTypeFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [isMineFilter, setIsMineFilter] = useState("");
  const [timeFilter, setTimeFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalResources, setTotalResources] = useState(0);
  const [selectedResources, setSelectedResources] = useState<number[]>([]);

  const loadResources = useCallback(async () => {
    setLoading(true);
    try {
      const result: AdminResourceListResponse = await getAdminResourceList(
        currentPage,
        20,
        searchTerm || undefined,
        panTypeFilter ? parseInt(panTypeFilter) : undefined,
        fileTypeFilter || undefined,
        statusFilter || undefined,
        isMineFilter ? isMineFilter === "true" : undefined,
        "updated_at",
        "desc",
        timeFilter
      );

      if (result.success) {
        setResources(result.data.resources);
        setTotalPages(result.data.total_pages);
        setTotalResources(result.data.total);
      } else {
        showToast(result.message || "获取资源列表失败", "error");
      }
    } catch {
      showToast("获取资源列表失败", "error");
    } finally {
      setLoading(false);
    }
  }, [
    currentPage,
    searchTerm,
    panTypeFilter,
    fileTypeFilter,
    statusFilter,
    isMineFilter,
    timeFilter,
    showToast,
  ]);

  useEffect(() => {
    loadResources();
  }, [loadResources]);

  const handleSearch = () => {
    setCurrentPage(1);
    loadResources();
  };

  const handleDeleteResource = async (resourceId: number, title: string) => {
    if (!confirm(`确定要删除资源 "${title}" 吗？此操作不可恢复。`)) {
      return;
    }

    try {
      const result = await deleteAdminResource(resourceId);
      if (result.success) {
        showToast(result.message, "success");
        loadResources();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("删除资源失败", "error");
    }
  };

  const handleBatchDelete = async () => {
    if (selectedResources.length === 0) {
      showToast("请选择要删除的资源", "error");
      return;
    }

    if (
      !confirm(
        `确定要删除选中的 ${selectedResources.length} 个资源吗？此操作不可恢复。`
      )
    ) {
      return;
    }

    try {
      const result = await batchDeleteAdminResources(selectedResources);
      if (result.success) {
        showToast(result.message, "success");
        setSelectedResources([]);
        loadResources();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("批量删除资源失败", "error");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  const getPanTypeName = (panType: number) => {
    const panTypeMap: { [key: number]: string } = {
      1: "百度网盘",
      2: "夸克网盘",
      3: "阿里云盘",
      4: "迅雷网盘",
    };
    return panTypeMap[panType] || "其他";
  };

  const getStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      valid: "有效",
      invalid: "失效",
      unknown: "未知",
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      valid: "text-green-600 dark:text-green-400",
      invalid: "text-red-600 dark:text-red-400",
      unknown: "text-yellow-600 dark:text-yellow-400",
    };
    return colorMap[status] || "text-gray-600 dark:text-gray-400";
  };

  const filters: FilterField[] = [
    {
      key: "panType",
      label: "网盘类型",
      type: "select",
      value: panTypeFilter,
      onChange: setPanTypeFilter,
      options: [
        { label: "百度网盘", value: "1" },
        { label: "夸克网盘", value: "2" },
        { label: "阿里云盘", value: "3" },
        { label: "迅雷网盘", value: "4" },
      ],
    },
    {
      key: "fileType",
      label: "文件类型",
      type: "select",
      value: fileTypeFilter,
      onChange: setFileTypeFilter,
      options: [
        { label: "视频", value: "video" },
        { label: "音频", value: "audio" },
        { label: "图片", value: "image" },
        { label: "文档", value: "document" },
        { label: "压缩包", value: "archive" },
        { label: "应用程序", value: "application" },
      ],
    },
    {
      key: "status",
      label: "资源状态",
      type: "select",
      value: statusFilter,
      onChange: setStatusFilter,
      options: [
        { label: "有效", value: "valid" },
        { label: "失效", value: "invalid" },
        { label: "未知", value: "unknown" },
      ],
    },
    {
      key: "isMine",
      label: "我的资源",
      type: "select",
      value: isMineFilter,
      onChange: setIsMineFilter,
      options: [
        { label: "是", value: "true" },
        { label: "否", value: "false" },
      ],
    },
    {
      key: "timeFilter",
      label: "时间筛选",
      type: "select",
      value: timeFilter,
      onChange: setTimeFilter,
      options: [
        { label: "全部时间", value: "all" },
        { label: "最近一周", value: "week" },
        { label: "最近半月", value: "half_month" },
        { label: "最近一月", value: "month" },
        { label: "最近半年", value: "half_year" },
        { label: "最近一年", value: "year" },
      ],
    },
  ];

  const columns: Column<AdminResource>[] = [
    {
      key: "select",
      title: "选择",
      width: "60px",
      render: (_, record) => (
        <input
          type="checkbox"
          checked={selectedResources.includes(record.id)}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedResources([...selectedResources, record.id]);
            } else {
              setSelectedResources(
                selectedResources.filter((id) => id !== record.id)
              );
            }
          }}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          aria-label={`选择资源 ${record.title}`}
          title={`选择资源 ${record.title}`}
        />
      ),
    },
    {
      key: "title",
      title: "资源信息",
      render: (_, record) => (
        <div>
          <div className="text-sm font-medium text-foreground truncate max-w-xs">
            {record.title}
          </div>
          <div className="text-xs text-secondary-text">
            {record.resource_key}
          </div>
        </div>
      ),
    },
    {
      key: "pan_type",
      title: "网盘类型",
      render: (panType) => (
        <span className="text-sm text-foreground">
          {getPanTypeName(panType)}
        </span>
      ),
    },
    {
      key: "file_type",
      title: "文件类型",
      render: (fileType) => (
        <span className="text-sm text-foreground">{fileType || "未知"}</span>
      ),
    },
    {
      key: "file_size",
      title: "文件大小",
      render: (fileSize) => (
        <span className="text-sm text-foreground">{fileSize || "未知"}</span>
      ),
    },
    {
      key: "status",
      title: "状态",
      render: (status) => (
        <span className={`text-sm ${getStatusColor(status)}`}>
          {getStatusText(status)}
        </span>
      ),
    },
    {
      key: "access_count",
      title: "访问次数",
      render: (count) => (
        <span className="text-sm text-foreground">{count || 0}</span>
      ),
    },
    {
      key: "updated_at",
      title: "更新时间",
      render: (date) => (
        <span className="text-sm text-secondary-text">{formatDate(date)}</span>
      ),
    },
    {
      key: "actions",
      title: "操作",
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() =>
              window.open(`/resources/${record.resource_key}`, "_blank")
            }
          >
            查看
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleDeleteResource(record.id, record.title)}
            className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
          >
            删除
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-foreground mb-2">资源管理</h1>
        <p className="text-secondary-text">管理网盘资源内容</p>
      </div>

      {/* 搜索和筛选 */}
      <SearchFilter
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        onSearch={handleSearch}
        filters={filters}
        actions={
          <Button
            onClick={handleBatchDelete}
            disabled={selectedResources.length === 0}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            批量删除 ({selectedResources.length})
          </Button>
        }
        className="mb-6"
      />

      {/* 资源列表 */}
      <DataTable
        columns={columns}
        data={resources}
        loading={loading}
        pagination={{
          current: currentPage,
          total: totalResources,
          pageSize: 20,
          onChange: setCurrentPage,
        }}
        emptyText="暂无资源数据"
      />
    </div>
  );
}
