"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import {
  UsersIcon,
  FolderIcon,
  ChatBubbleLeftRightIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { useToast } from "@/components/ToastProvider";

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalResources: number;
  validResources: number;
  pendingFeedback: number;
  todayVisits: number;
}

interface StatCard {
  title: string;
  value: string | number;
  change?: string;
  changeType?: "increase" | "decrease" | "neutral";
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  color: string;
}

export default function AdminDashboard() {
  const { showToast } = useToast();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalResources: 0,
    validResources: 0,
    pendingFeedback: 0,
    todayVisits: 0,
  });
  const [loading, setLoading] = useState(true);

  const loadDashboardStats = useCallback(async () => {
    try {
      setLoading(true);
      // 这里应该调用实际的API获取统计数据
      // 暂时使用模拟数据
      setTimeout(() => {
        setStats({
          totalUsers: 1234,
          activeUsers: 856,
          totalResources: 45678,
          validResources: 42341,
          pendingFeedback: 23,
          todayVisits: 2847,
        });
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error("加载仪表盘数据失败:", error);
      showToast("加载仪表盘数据失败", "error");
      setLoading(false);
    }
  }, [showToast]);

  useEffect(() => {
    loadDashboardStats();
  }, [loadDashboardStats]);

  const statCards: StatCard[] = [
    {
      title: "总用户数",
      value: stats.totalUsers.toLocaleString(),
      change: "+12%",
      changeType: "increase",
      icon: UsersIcon,
      href: "/admin/users",
      color: "from-blue-600 to-blue-700",
    },
    {
      title: "活跃用户",
      value: stats.activeUsers.toLocaleString(),
      change: "+8%",
      changeType: "increase",
      icon: CheckCircleIcon,
      href: "/admin/users?status=active",
      color: "from-green-600 to-green-700",
    },
    {
      title: "总资源数",
      value: stats.totalResources.toLocaleString(),
      change: "+156",
      changeType: "increase",
      icon: FolderIcon,
      href: "/admin/resources",
      color: "from-purple-600 to-purple-700",
    },
    {
      title: "有效资源",
      value: stats.validResources.toLocaleString(),
      change: "-23",
      changeType: "decrease",
      icon: ChartBarIcon,
      href: "/admin/resources?status=valid",
      color: "from-indigo-600 to-indigo-700",
    },
    {
      title: "待处理反馈",
      value: stats.pendingFeedback,
      change: stats.pendingFeedback > 0 ? "需要处理" : "全部处理完成",
      changeType: stats.pendingFeedback > 0 ? "neutral" : "increase",
      icon: ChatBubbleLeftRightIcon,
      href: "/admin/feedback",
      color:
        stats.pendingFeedback > 0
          ? "from-orange-600 to-orange-700"
          : "from-green-600 to-green-700",
    },
    {
      title: "今日访问",
      value: stats.todayVisits.toLocaleString(),
      change: "+15%",
      changeType: "increase",
      icon: ExclamationTriangleIcon,
      color: "from-pink-600 to-pink-700",
    },
  ];

  const getChangeColor = (type?: "increase" | "decrease" | "neutral") => {
    switch (type) {
      case "increase":
        return "text-green-600 dark:text-green-400";
      case "decrease":
        return "text-red-600 dark:text-red-400";
      case "neutral":
        return "text-yellow-600 dark:text-yellow-400";
      default:
        return "text-secondary-text";
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-hover-background rounded w-48 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="bg-card-background rounded-lg p-6 border border-border-color"
              >
                <div className="h-4 bg-hover-background rounded w-24 mb-4"></div>
                <div className="h-8 bg-hover-background rounded w-16 mb-2"></div>
                <div className="h-3 bg-hover-background rounded w-20"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-foreground mb-2">
          管理后台仪表盘
        </h1>
        <p className="text-secondary-text">系统概览和关键指标监控</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {statCards.map((card, index) => (
          <div
            key={index}
            className="bg-card-background rounded-lg shadow-lg border border-border-color overflow-hidden hover:shadow-xl transition-shadow"
          >
            {card.href ? (
              <Link href={card.href} className="block p-6">
                <div className="flex items-center">
                  <div
                    className={`p-3 rounded-lg bg-gradient-to-r ${card.color} mr-4`}
                  >
                    <card.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-secondary-text mb-1">
                      {card.title}
                    </p>
                    <p className="text-2xl font-bold text-foreground mb-1">
                      {card.value}
                    </p>
                    {card.change && (
                      <p
                        className={`text-sm ${getChangeColor(card.changeType)}`}
                      >
                        {card.change}
                      </p>
                    )}
                  </div>
                </div>
              </Link>
            ) : (
              <div className="p-6">
                <div className="flex items-center">
                  <div
                    className={`p-3 rounded-lg bg-gradient-to-r ${card.color} mr-4`}
                  >
                    <card.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-secondary-text mb-1">
                      {card.title}
                    </p>
                    <p className="text-2xl font-bold text-foreground mb-1">
                      {card.value}
                    </p>
                    {card.change && (
                      <p
                        className={`text-sm ${getChangeColor(card.changeType)}`}
                      >
                        {card.change}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 快速操作 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 最近活动 */}
        <div className="bg-card-background rounded-lg shadow-lg border border-border-color p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">
            最近活动
          </h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm text-foreground">新用户注册</p>
                <p className="text-xs text-secondary-text">2分钟前</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm text-foreground">资源上传</p>
                <p className="text-xs text-secondary-text">5分钟前</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm text-foreground">用户反馈</p>
                <p className="text-xs text-secondary-text">10分钟前</p>
              </div>
            </div>
          </div>
        </div>

        {/* 系统状态 */}
        <div className="bg-card-background rounded-lg shadow-lg border border-border-color p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">
            系统状态
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-foreground">服务器状态</span>
              <span className="text-sm text-green-600 dark:text-green-400">
                正常
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-foreground">数据库连接</span>
              <span className="text-sm text-green-600 dark:text-green-400">
                正常
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-foreground">存储空间</span>
              <span className="text-sm text-yellow-600 dark:text-yellow-400">
                75%
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-foreground">API响应时间</span>
              <span className="text-sm text-green-600 dark:text-green-400">
                120ms
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
