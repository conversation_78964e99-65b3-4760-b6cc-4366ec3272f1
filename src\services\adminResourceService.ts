const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

export interface AdminResource {
  id: number;
  resource_key: string;
  title: string;
  pan_type: number;
  file_type?: string;
  file_size?: string;
  status: string;
  created_at: string;
  updated_at: string;
  access_count: number;
  author?: string;
  is_mine: boolean;
  verified_status?: string;
}

export interface AdminResourceListResponse {
  success: boolean;
  message: string;
  data: {
    resources: AdminResource[];
    total: number;
    total_pages: number;
    current_page: number;
  };
  error?: string;
}

export interface AdminResourceDetailResponse {
  success: boolean;
  message: string;
  data: AdminResource;
  error?: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  error?: string;
}

/**
 * 获取管理员资源列表
 */
export async function getAdminResourceList(
  page: number = 1,
  size: number = 20,
  keyword?: string,
  panType?: number,
  fileType?: string,
  status?: string,
  isMine?: boolean,
  sortBy: string = "updated_at",
  sortOrder: string = "desc",
  timeFilter: string = "all"
): Promise<AdminResourceListResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录",
        data: { resources: [], total: 0, total_pages: 0, current_page: 1 },
        error: "需要管理员权限",
      };
    }

    const params = new URLSearchParams();
    params.append("page", page.toString());
    params.append("size", size.toString());
    params.append("sort_by", sortBy);
    params.append("sort_order", sortOrder);
    params.append("time_filter", timeFilter);

    if (keyword) params.append("keyword", keyword);
    if (panType !== undefined) params.append("pan_type", panType.toString());
    if (fileType) params.append("file_type", fileType);
    if (status) params.append("status", status);
    if (isMine !== undefined) params.append("is_mine", isMine.toString());

    const response = await fetch(`${API_BASE_URL}/api/admin/resources?${params}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "获取资源列表失败",
        data: { resources: [], total: 0, total_pages: 0, current_page: 1 },
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "获取资源列表成功",
      data: data.data || data,
    };
  } catch (error) {
    console.error("获取管理员资源列表失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      data: { resources: [], total: 0, total_pages: 0, current_page: 1 },
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 获取管理员资源详情
 */
export async function getAdminResourceDetail(
  resourceKey: string
): Promise<AdminResourceDetailResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录",
        data: {} as AdminResource,
        error: "需要管理员权限",
      };
    }

    const response = await fetch(
      `${API_BASE_URL}/api/admin/resources/${resourceKey}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "获取资源详情失败",
        data: {} as AdminResource,
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "获取资源详情成功",
      data: data.data || data,
    };
  } catch (error) {
    console.error("获取管理员资源详情失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      data: {} as AdminResource,
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 删除资源
 */
export async function deleteAdminResource(resourceId: number): Promise<AuthResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录",
        error: "需要管理员权限",
      };
    }

    const response = await fetch(
      `${API_BASE_URL}/api/admin/resources/${resourceId}`,
      {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "删除资源失败",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "资源删除成功",
    };
  } catch (error) {
    console.error("删除资源失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 批量删除资源
 */
export async function batchDeleteAdminResources(
  resourceIds: number[]
): Promise<AuthResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录",
        error: "需要管理员权限",
      };
    }

    const response = await fetch(
      `${API_BASE_URL}/api/admin/resources/batch-delete`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ resource_ids: resourceIds }),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "批量删除资源失败",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "资源批量删除成功",
    };
  } catch (error) {
    console.error("批量删除资源失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 获取资源统计信息
 */
export async function getAdminResourceStats(): Promise<any> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录",
        error: "需要管理员权限",
      };
    }

    const response = await fetch(`${API_BASE_URL}/api/admin/resources/stats`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "获取资源统计失败",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "获取资源统计成功",
      data: data.data || data,
    };
  } catch (error) {
    console.error("获取资源统计失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}
