"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/components/ToastProvider";
import { login, LoginRequest } from "@/services/authService";
import PageHeader from "@/components/PageHeader";
import { shouldShowRegistrationInLogin } from "@/config/features";

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { showToast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // 检查是否允许显示注册功能
  // 优先级：URL参数 > 配置文件设置
  const urlShowRegister = searchParams?.get("showRegister") === "true";
  const configShowRegister = shouldShowRegistrationInLogin();
  const showRegister = urlShowRegister || configShowRegister;
  const [formData, setFormData] = useState<LoginRequest>({
    username: "",
    password: "",
    remember_me: false,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.username.trim()) {
      showToast("请输入用户名或邮箱", "error");
      return;
    }

    if (!formData.password.trim()) {
      showToast("请输入密码", "error");
      return;
    }

    setIsLoading(true);

    try {
      const result = await login(formData);

      if (result.success) {
        showToast(result.message, "success");
        // 登录成功后跳转到首页或用户指定的页面
        const redirectTo =
          new URLSearchParams(window.location.search).get("redirect") || "/";
        router.push(redirectTo);
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("登录失败，请稍后重试", "error");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <PageHeader title="用户登录" description="登录您的账户以访问更多功能" />

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          <div className="bg-card-background rounded-lg shadow-lg p-8 border border-border-color">
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-foreground mb-2">
                欢迎回来
              </h1>
              <p className="text-secondary-text">请登录您的账户</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label
                  htmlFor="username"
                  className="block text-sm font-medium text-foreground mb-2"
                >
                  用户名或邮箱
                </label>
                <Input
                  id="username"
                  name="username"
                  type="text"
                  value={formData.username}
                  onChange={handleInputChange}
                  placeholder="请输入用户名或邮箱"
                  required
                  className="w-full"
                />
              </div>

              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-foreground mb-2"
                >
                  密码
                </label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="请输入密码"
                  required
                  className="w-full"
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember_me"
                    name="remember_me"
                    type="checkbox"
                    checked={formData.remember_me}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-button-background focus:ring-button-background border-border-color rounded"
                  />
                  <label
                    htmlFor="remember_me"
                    className="ml-2 block text-sm text-secondary-text"
                  >
                    记住我
                  </label>
                </div>

                <Link
                  href="/forgot-password"
                  className="text-sm text-link-color hover:text-button-hover transition-colors"
                >
                  忘记密码？
                </Link>
              </div>

              <Button
                type="submit"
                disabled={isLoading}
                className="w-full"
                size="lg"
              >
                {isLoading ? "登录中..." : "登录"}
              </Button>
            </form>

            {showRegister && (
              <div className="mt-8 text-center">
                <p className="text-secondary-text">
                  还没有账户？{" "}
                  <Link
                    href="/register"
                    className="text-link-color hover:text-button-hover font-medium transition-colors"
                  >
                    立即注册
                  </Link>
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
