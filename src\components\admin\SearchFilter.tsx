"use client";

import { ReactNode } from "react";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { MagnifyingGlassIcon, FunnelIcon } from "@heroicons/react/24/outline";

export interface FilterOption {
  label: string;
  value: string;
}

export interface FilterField {
  key: string;
  label: string;
  type: "select" | "input" | "date";
  options?: FilterOption[];
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
}

export interface SearchFilterProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  onSearch: () => void;
  filters?: FilterField[];
  actions?: ReactNode;
  className?: string;
}

export default function SearchFilter({
  searchValue,
  onSearchChange,
  onSearch,
  filters = [],
  actions,
  className = "",
}: SearchFilterProps) {
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      onSearch();
    }
  };

  return (
    <div className={`bg-card-background rounded-lg shadow-lg p-6 border border-border-color ${className}`}>
      <div className="space-y-4">
        {/* 搜索栏 */}
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-secondary-text" />
            </div>
            <Input
              type="text"
              placeholder="输入关键词搜索..."
              value={searchValue}
              onChange={(e) => onSearchChange(e.target.value)}
              onKeyPress={handleKeyPress}
              className="pl-10"
            />
          </div>
          <Button onClick={onSearch} className="flex items-center space-x-2">
            <MagnifyingGlassIcon className="h-4 w-4" />
            <span>搜索</span>
          </Button>
        </div>

        {/* 筛选条件 */}
        {filters.length > 0 && (
          <div className="border-t border-border-color pt-4">
            <div className="flex items-center space-x-2 mb-3">
              <FunnelIcon className="h-4 w-4 text-secondary-text" />
              <span className="text-sm font-medium text-foreground">筛选条件</span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filters.map((filter) => (
                <div key={filter.key}>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    {filter.label}
                  </label>
                  {filter.type === "select" ? (
                    <select
                      value={filter.value}
                      onChange={(e) => filter.onChange(e.target.value)}
                      className="w-full px-4 py-2 rounded-md border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-foreground bg-white dark:bg-[#2c2c34] dark:border-[#4b4d61]"
                    >
                      <option value="">{filter.placeholder || `全部${filter.label}`}</option>
                      {filter.options?.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  ) : filter.type === "date" ? (
                    <Input
                      type="date"
                      value={filter.value}
                      onChange={(e) => filter.onChange(e.target.value)}
                      placeholder={filter.placeholder}
                    />
                  ) : (
                    <Input
                      type="text"
                      value={filter.value}
                      onChange={(e) => filter.onChange(e.target.value)}
                      placeholder={filter.placeholder}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        {actions && (
          <div className="border-t border-border-color pt-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-secondary-text">
                操作
              </div>
              <div className="flex items-center space-x-2">
                {actions}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
